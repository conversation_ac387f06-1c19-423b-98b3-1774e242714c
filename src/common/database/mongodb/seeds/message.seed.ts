import { User } from '@modules/users/entities/user.entity';
import {
  Message,
  MessageDocument,
  ThreadDocument,
} from '@modules/threads/entities/thread.entity';

interface ThreadData extends ThreadDocument {
  _id: string;
}

export async function seedMessages(threads: ThreadData[], users: User[]) {
  console.log('🌱 Seeding messages...');

  // Check if messages already exist
  const existingMessages = await Message.find();
  if (existingMessages.length > 0) {
    console.log('✅ Messages already exist, skipping message seeding');
    return existingMessages;
  }

  const messagesData: Array<{
    threadId: string;
    senderId: string;
    content: string;
    timestamp: Date;
  }> = [];

  // Generate messages for each thread
  for (let i = 0; i < threads.length; i++) {
    const thread = threads[i];
    const threadUsers = users.filter((u) =>
      thread.threadUserIds.includes(u.id),
    );

    if (threadUsers.length === 0) continue;

    // Generate 5-10 messages per thread
    const messageCount = Math.floor(Math.random() * 6) + 5; // 5-10 messages
    const baseDate = new Date('2024-01-15T09:00:00Z');

    for (let j = 0; j < messageCount; j++) {
      const sender = threadUsers[j % threadUsers.length];
      const messageDate = new Date(
        baseDate.getTime() + i * 24 * 60 * 60 * 1000 + j * 2 * 60 * 60 * 1000,
      ); // Spread messages over time

      let content = '';

      // Generate realistic content based on thread title and message position
      if (j === 0) {
        // First message - usually from thread creator
        content = generateFirstMessage(thread.title);
      } else {
        // Subsequent messages - varied responses
        content = generateSubsequentMessage(thread.title);
      }

      messagesData.push({
        threadId: thread._id,
        senderId: sender.id,
        content: content,
        timestamp: messageDate,
      });
    }
  }

  // Save all messages
  const savedMessages: MessageDocument[] = [];
  for (const messageData of messagesData) {
    const message = new Message(messageData);
    const savedMessage = await message.save();
    savedMessages.push(savedMessage);
  }

  console.log(`✅ Created ${savedMessages.length} messages`);
  return savedMessages;
}

function generateFirstMessage(threadTitle: string): string {
  if (threadTitle.includes('Planning')) {
    return `Welcome everyone! Let's start planning our approach for this ${threadTitle.toLowerCase()}. What are your initial thoughts?`;
  } else if (threadTitle.includes('Development')) {
    return `Hi team! I've created this thread to coordinate our development efforts. Let's discuss our strategy.`;
  } else if (threadTitle.includes('Database')) {
    return `Let's begin by discussing the database requirements and schema design for our project.`;
  } else if (threadTitle.includes('Testing')) {
    return `Time to focus on testing! What testing frameworks and strategies should we use?`;
  } else if (threadTitle.includes('ML Model')) {
    return `Let's discuss our machine learning approach and model selection criteria.`;
  } else {
    return `Welcome to the ${threadTitle} discussion! Looking forward to collaborating with everyone.`;
  }
}

function generateSubsequentMessage(threadTitle: string): string {
  const responses = [
    `Great point! I think we should also consider the scalability aspects.`,
    `I agree with the previous suggestions. Here's what I think we should add...`,
    `That's a solid approach. Should we also look into performance optimization?`,
    `Thanks for sharing that resource! It's very helpful for our implementation.`,
    `I've been working on this part and encountered an interesting challenge...`,
    `Good progress everyone! Here's an update on my assigned tasks.`,
    `I found a useful library that might help us with this functionality.`,
    `Let me share some documentation that could be relevant to our discussion.`,
    `Has anyone tried implementing this feature yet? I'd love to hear about your experience.`,
    `I think we need to revisit our initial assumptions about this requirement.`,
    `The testing results look promising. Here are the metrics I gathered...`,
    `I've pushed some code changes. Could someone review them when you have time?`,
    `We might want to consider an alternative approach for better maintainability.`,
    `Great collaboration so far! I'm impressed with our team's progress.`,
    `I'll take care of the documentation for this feature by tomorrow.`,
    `Should we schedule a quick sync meeting to discuss the next steps?`,
    `I've updated the project board with our latest progress and blockers.`,
    `The integration is working well. Here are some screenshots of the results.`,
    `I think we're ready to move to the next phase of development.`,
    `Thanks everyone for the productive discussion! Let's implement these ideas.`,
  ];

  let content = responses[Math.floor(Math.random() * responses.length)];

  // Add some context-specific content
  if (threadTitle.includes('Frontend') && Math.random() > 0.7) {
    content += ` I'm thinking we should use React hooks for state management.`;
  } else if (threadTitle.includes('Backend') && Math.random() > 0.7) {
    content += ` The API endpoints are looking good so far.`;
  } else if (threadTitle.includes('Database') && Math.random() > 0.7) {
    content += ` We should ensure proper indexing for performance.`;
  } else if (threadTitle.includes('Testing') && Math.random() > 0.7) {
    content += ` Unit tests are passing, now working on integration tests.`;
  }

  return content;
}
